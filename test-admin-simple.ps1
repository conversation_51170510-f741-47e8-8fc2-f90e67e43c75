Write-Host "Testing admin login..." -ForegroundColor Yellow

try {
    $body = '{"username":"root","password":"admin123"}'
    $response = Invoke-RestMethod -Uri "http://localhost:8081/admin/login" -Method POST -ContentType "application/json" -Body $body
    
    Write-Host "Login Success!" -ForegroundColor Green
    Write-Host "Username: $($response.data.username)" -ForegroundColor Green
    Write-Host "Role: $($response.data.role)" -ForegroundColor Green
    Write-Host "Token: $($response.data.token.Substring(0,20))..." -ForegroundColor Green
    
    $token = $response.data.token
    
    # Test statistics
    Write-Host "`nTesting statistics..." -ForegroundColor Yellow
    $headers = @{ "X-Auth-Token" = $token }
    $statsResponse = Invoke-RestMethod -Uri "http://localhost:8081/admin/statistics" -Method GET -Headers $headers
    
    Write-Host "Statistics Success!" -ForegroundColor Green
    Write-Host "Total Users: $($statsResponse.data.totalUsers)" -ForegroundColor Green
    Write-Host "Active Users: $($statsResponse.data.activeUsers)" -ForegroundColor Green
    Write-Host "VIP Users: $($statsResponse.data.totalVipUsers)" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
}
