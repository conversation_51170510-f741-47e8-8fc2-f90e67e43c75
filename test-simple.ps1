Write-Host "Testing UserManager System..." -ForegroundColor Green

# Test backend health
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
    Write-Host "Backend Health: OK" -ForegroundColor Green
} catch {
    Write-Host "Backend Health: FAILED" -ForegroundColor Red
}

# Test admin login
try {
    $body = '{"username":"root","password":"admin123"}'
    $admin = Invoke-RestMethod -Uri "http://localhost:8081/admin/login" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Admin Login: OK" -ForegroundColor Green
    $token = $admin.data.token
} catch {
    Write-Host "Admin Login: FAILED" -ForegroundColor Red
}

# Test statistics
if ($token) {
    try {
        $headers = @{ "X-Auth-Token" = $token }
        $stats = Invoke-RestMethod -Uri "http://localhost:8081/admin/statistics" -Method GET -Headers $headers
        Write-Host "Statistics: OK - Total Users: $($stats.data.totalUsers)" -ForegroundColor Green
    } catch {
        Write-Host "Statistics: FAILED" -ForegroundColor Red
    }
}

# Test frontend
try {
    $frontend = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -UseBasicParsing
    if ($frontend.StatusCode -eq 200) {
        Write-Host "Frontend: OK" -ForegroundColor Green
    }
} catch {
    Write-Host "Frontend: FAILED" -ForegroundColor Red
}

Write-Host "`nSystem Status:" -ForegroundColor Yellow
Write-Host "Backend API: http://localhost:8081" -ForegroundColor Cyan
Write-Host "Frontend UI: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Admin Login: root / admin123" -ForegroundColor Cyan
