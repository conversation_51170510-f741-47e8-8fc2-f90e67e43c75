# 管理员API测试脚本

Write-Host "=== 管理员API测试开始 ===" -ForegroundColor Green

# 1. 测试管理员登录
Write-Host "`n1. 测试管理员登录..." -ForegroundColor Yellow
try {
    $adminLoginBody = @{
        username = "root"
        password = "admin123"
    } | ConvertTo-Json
    
    $adminLoginResponse = Invoke-WebRequest -Uri "http://localhost:8081/admin/login" -Method POST -ContentType "application/json" -Body $adminLoginBody -UseBasicParsing
    Write-Host "管理员登录状态码: $($adminLoginResponse.StatusCode)" -ForegroundColor Green
    
    $adminLoginJson = $adminLoginResponse.Content | ConvertFrom-Json
    Write-Host "登录结果: $($adminLoginJson.msg)" -ForegroundColor Green
    Write-Host "管理员用户名: $($adminLoginJson.data.username)" -ForegroundColor Green
    Write-Host "角色: $($adminLoginJson.data.role)" -ForegroundColor Green
    
    # 保存管理员token
    $adminToken = $adminLoginJson.data.token
    Write-Host "管理员Token已获取: $($adminToken.Substring(0, 20))..." -ForegroundColor Green
    
} catch {
    Write-Host "管理员登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试获取统计信息
Write-Host "`n2. 测试获取统计信息..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-Auth-Token" = $adminToken
    }
    
    $statisticsResponse = Invoke-WebRequest -Uri "http://localhost:8081/admin/statistics" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "统计信息查询状态码: $($statisticsResponse.StatusCode)" -ForegroundColor Green
    
    $statisticsJson = $statisticsResponse.Content | ConvertFrom-Json
    Write-Host "总用户数: $($statisticsJson.data.totalUsers)" -ForegroundColor Green
    Write-Host "活跃用户数: $($statisticsJson.data.activeUsers)" -ForegroundColor Green
    Write-Host "VIP用户数: $($statisticsJson.data.totalVipUsers)" -ForegroundColor Green
    Write-Host "有效VIP用户数: $($statisticsJson.data.validVipUsers)" -ForegroundColor Green
    Write-Host "过期VIP用户数: $($statisticsJson.data.expiredVipUsers)" -ForegroundColor Green
    
} catch {
    Write-Host "获取统计信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试获取用户列表
Write-Host "`n3. 测试获取用户列表..." -ForegroundColor Yellow
try {
    $usersResponse = Invoke-WebRequest -Uri "http://localhost:8081/admin/users" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "用户列表查询状态码: $($usersResponse.StatusCode)" -ForegroundColor Green
    
    $usersJson = $usersResponse.Content | ConvertFrom-Json
    Write-Host "用户列表长度: $($usersJson.data.Count)" -ForegroundColor Green
    
    if ($usersJson.data.Count -gt 0) {
        Write-Host "第一个用户ID: $($usersJson.data[0].userId)" -ForegroundColor Green
        Write-Host "第一个用户激活码: $($usersJson.data[0].activationCode)" -ForegroundColor Green
    }
    
} catch {
    Write-Host "获取用户列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试错误的管理员登录
Write-Host "`n4. 测试错误的管理员登录..." -ForegroundColor Yellow
try {
    $wrongLoginBody = @{
        username = "admin"
        password = "wrong"
    } | ConvertTo-Json
    
    $wrongLoginResponse = Invoke-WebRequest -Uri "http://localhost:8081/admin/login" -Method POST -ContentType "application/json" -Body $wrongLoginBody -UseBasicParsing
    Write-Host "错误登录测试意外成功" -ForegroundColor Red
    
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "错误登录验证正常工作 - 返回400错误" -ForegroundColor Green
    } else {
        Write-Host "错误登录测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. 测试无权限访问
Write-Host "`n5. 测试无权限访问..." -ForegroundColor Yellow
try {
    $noAuthHeaders = @{
        "X-Auth-Token" = "invalid_token"
    }
    
    $noAuthResponse = Invoke-WebRequest -Uri "http://localhost:8081/admin/statistics" -Method GET -Headers $noAuthHeaders -UseBasicParsing
    Write-Host "无权限访问测试意外成功" -ForegroundColor Red
    
} catch {
    if ($_.Exception.Response.StatusCode -eq 403) {
        Write-Host "权限验证正常工作 - 返回403错误" -ForegroundColor Green
    } else {
        Write-Host "权限验证测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 管理员API测试完成 ===" -ForegroundColor Green
