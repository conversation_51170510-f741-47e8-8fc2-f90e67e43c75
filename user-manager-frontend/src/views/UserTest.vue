<template>
  <div class="user-test">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户登录测试</span>
          </template>
          
          <el-form :model="loginForm" label-width="100px">
            <el-form-item label="激活码">
              <el-input 
                v-model="loginForm.card" 
                placeholder="请输入激活码"
              />
            </el-form-item>
            <el-form-item label="代理">
              <el-input 
                v-model="loginForm.agent" 
                placeholder="请输入代理标识"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testLogin"
                :loading="loginLoading"
              >
                测试登录
              </el-button>
              <el-button @click="clearLoginResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="loginResult" class="result-section">
            <h4>登录结果：</h4>
            <el-alert
              :title="loginResult.success ? '登录成功' : '登录失败'"
              :type="loginResult.success ? 'success' : 'error'"
              :description="loginResult.message"
              show-icon
            />
            <div v-if="loginResult.data" class="result-data">
              <pre>{{ JSON.stringify(loginResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户信息查询测试</span>
          </template>
          
          <el-form :model="whoamiForm" label-width="100px">
            <el-form-item label="Token">
              <el-input 
                v-model="whoamiForm.token" 
                placeholder="请输入用户Token"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testWhoami"
                :loading="whoamiLoading"
              >
                查询用户信息
              </el-button>
              <el-button @click="clearWhoamiResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="whoamiResult" class="result-section">
            <h4>查询结果：</h4>
            <el-alert
              :title="whoamiResult.success ? '查询成功' : '查询失败'"
              :type="whoamiResult.success ? 'success' : 'error'"
              :description="whoamiResult.message"
              show-icon
            />
            <div v-if="whoamiResult.data" class="result-data">
              <pre>{{ JSON.stringify(whoamiResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户登出测试</span>
          </template>
          
          <el-form :model="logoutForm" label-width="100px">
            <el-form-item label="Token">
              <el-input 
                v-model="logoutForm.token" 
                placeholder="请输入用户Token"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testLogout"
                :loading="logoutLoading"
              >
                测试登出
              </el-button>
              <el-button @click="clearLogoutResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="logoutResult" class="result-section">
            <h4>登出结果：</h4>
            <el-alert
              :title="logoutResult.success ? '登出成功' : '登出失败'"
              :type="logoutResult.success ? 'success' : 'error'"
              :description="logoutResult.message"
              show-icon
            />
            <div v-if="logoutResult.data" class="result-data">
              <pre>{{ JSON.stringify(logoutResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>健康检查测试</span>
          </template>
          
          <el-form label-width="100px">
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testHealth"
                :loading="healthLoading"
              >
                检查系统健康状态
              </el-button>
              <el-button @click="clearHealthResult">清空结果</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="healthResult" class="result-section">
            <h4>健康检查结果：</h4>
            <el-alert
              :title="healthResult.success ? '系统正常' : '系统异常'"
              :type="healthResult.success ? 'success' : 'error'"
              :description="healthResult.message"
              show-icon
            />
            <div v-if="healthResult.data" class="result-data">
              <pre>{{ JSON.stringify(healthResult.data, null, 2) }}</pre>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>快速测试</span>
          </template>
          
          <div class="quick-test">
            <el-button 
              type="success" 
              @click="runFullTest"
              :loading="fullTestLoading"
            >
              运行完整测试流程
            </el-button>
            <el-button @click="clearAllResults">清空所有结果</el-button>
          </div>
          
          <div v-if="fullTestResult" class="result-section">
            <h4>完整测试结果：</h4>
            <div v-for="(step, index) in fullTestResult" :key="index" class="test-step">
              <el-alert
                :title="step.name"
                :type="step.success ? 'success' : 'error'"
                :description="step.message"
                show-icon
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref } from 'vue'
import api from '@/utils/api'
import axios from 'axios'
import { ElMessage } from 'element-plus'

export default {
  name: 'UserTest',
  setup() {
    // 登录测试
    const loginForm = ref({
      card: 'test123456789',
      agent: 'main'
    })
    const loginLoading = ref(false)
    const loginResult = ref(null)
    
    // 用户信息查询测试
    const whoamiForm = ref({
      token: ''
    })
    const whoamiLoading = ref(false)
    const whoamiResult = ref(null)
    
    // 登出测试
    const logoutForm = ref({
      token: ''
    })
    const logoutLoading = ref(false)
    const logoutResult = ref(null)
    
    // 健康检查测试
    const healthLoading = ref(false)
    const healthResult = ref(null)
    
    // 完整测试
    const fullTestLoading = ref(false)
    const fullTestResult = ref(null)
    
    const testLogin = async () => {
      loginLoading.value = true
      try {
        const response = await api.post('/users/card-login', loginForm.value)
        loginResult.value = {
          success: true,
          message: '登录成功',
          data: response.data.data
        }
        
        // 自动填充token到其他测试表单
        if (response.data.data.token) {
          whoamiForm.value.token = response.data.data.token
          logoutForm.value.token = response.data.data.token
        }
        
        ElMessage.success('登录测试成功')
      } catch (error) {
        loginResult.value = {
          success: false,
          message: error.message || '登录失败',
          data: error.response?.data
        }
      } finally {
        loginLoading.value = false
      }
    }
    
    const testWhoami = async () => {
      if (!whoamiForm.value.token) {
        ElMessage.warning('请先输入Token')
        return
      }
      
      whoamiLoading.value = true
      try {
        const response = await axios.post('/api/users/whoami', {}, {
          headers: {
            'X-Auth-Token': whoamiForm.value.token
          }
        })
        whoamiResult.value = {
          success: true,
          message: '查询成功',
          data: response.data.data
        }
        ElMessage.success('用户信息查询成功')
      } catch (error) {
        whoamiResult.value = {
          success: false,
          message: error.message || '查询失败',
          data: error.response?.data
        }
      } finally {
        whoamiLoading.value = false
      }
    }
    
    const testLogout = async () => {
      if (!logoutForm.value.token) {
        ElMessage.warning('请先输入Token')
        return
      }
      
      logoutLoading.value = true
      try {
        const response = await axios.post('/api/users/logout', {}, {
          headers: {
            'X-Auth-Token': logoutForm.value.token
          }
        })
        logoutResult.value = {
          success: true,
          message: '登出成功',
          data: response.data.data
        }
        ElMessage.success('登出测试成功')
      } catch (error) {
        logoutResult.value = {
          success: false,
          message: error.message || '登出失败',
          data: error.response?.data
        }
      } finally {
        logoutLoading.value = false
      }
    }
    
    const testHealth = async () => {
      healthLoading.value = true
      try {
        const response = await api.get('/health')
        healthResult.value = {
          success: true,
          message: '系统健康状态正常',
          data: response.data.data
        }
        ElMessage.success('健康检查成功')
      } catch (error) {
        healthResult.value = {
          success: false,
          message: error.message || '健康检查失败',
          data: error.response?.data
        }
      } finally {
        healthLoading.value = false
      }
    }
    
    const runFullTest = async () => {
      fullTestLoading.value = true
      fullTestResult.value = []
      
      try {
        // 1. 健康检查
        try {
          await api.get('/health')
          fullTestResult.value.push({
            name: '1. 健康检查',
            success: true,
            message: '系统健康状态正常'
          })
        } catch (error) {
          fullTestResult.value.push({
            name: '1. 健康检查',
            success: false,
            message: '健康检查失败: ' + error.message
          })
        }
        
        // 2. 用户登录
        let userToken = null
        try {
          const response = await api.post('/users/card-login', {
            card: 'fulltest' + Date.now(),
            agent: 'main'
          })
          userToken = response.data.data.token
          fullTestResult.value.push({
            name: '2. 用户登录',
            success: true,
            message: '用户登录成功'
          })
        } catch (error) {
          fullTestResult.value.push({
            name: '2. 用户登录',
            success: false,
            message: '用户登录失败: ' + error.message
          })
        }
        
        // 3. 用户信息查询
        if (userToken) {
          try {
            await axios.post('/api/users/whoami', {}, {
              headers: { 'X-Auth-Token': userToken }
            })
            fullTestResult.value.push({
              name: '3. 用户信息查询',
              success: true,
              message: '用户信息查询成功'
            })
          } catch (error) {
            fullTestResult.value.push({
              name: '3. 用户信息查询',
              success: false,
              message: '用户信息查询失败: ' + error.message
            })
          }
          
          // 4. 用户登出
          try {
            await axios.post('/api/users/logout', {}, {
              headers: { 'X-Auth-Token': userToken }
            })
            fullTestResult.value.push({
              name: '4. 用户登出',
              success: true,
              message: '用户登出成功'
            })
          } catch (error) {
            fullTestResult.value.push({
              name: '4. 用户登出',
              success: false,
              message: '用户登出失败: ' + error.message
            })
          }
        }
        
        ElMessage.success('完整测试流程执行完成')
      } catch (error) {
        ElMessage.error('测试流程执行失败')
      } finally {
        fullTestLoading.value = false
      }
    }
    
    const clearLoginResult = () => { loginResult.value = null }
    const clearWhoamiResult = () => { whoamiResult.value = null }
    const clearLogoutResult = () => { logoutResult.value = null }
    const clearHealthResult = () => { healthResult.value = null }
    const clearAllResults = () => {
      loginResult.value = null
      whoamiResult.value = null
      logoutResult.value = null
      healthResult.value = null
      fullTestResult.value = null
    }
    
    return {
      loginForm,
      loginLoading,
      loginResult,
      whoamiForm,
      whoamiLoading,
      whoamiResult,
      logoutForm,
      logoutLoading,
      logoutResult,
      healthLoading,
      healthResult,
      fullTestLoading,
      fullTestResult,
      testLogin,
      testWhoami,
      testLogout,
      testHealth,
      runFullTest,
      clearLoginResult,
      clearWhoamiResult,
      clearLogoutResult,
      clearHealthResult,
      clearAllResults
    }
  }
}
</script>

<style scoped>
.user-test {
  padding: 0;
}

.result-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.result-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.result-data {
  margin-top: 10px;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.quick-test {
  margin-bottom: 20px;
}

.test-step {
  margin-bottom: 10px;
}
</style>
