package com.cet.aug.usermanager.controller;

import com.cet.aug.usermanager.dto.ApiResponse;
import com.cet.aug.usermanager.dto.LoginRequest;
import com.cet.aug.usermanager.entity.User;
import com.cet.aug.usermanager.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 激活码登录接口
     * POST /users/card-login
     */
    @PostMapping("/card-login")
    public ResponseEntity<ApiResponse<User>> cardLogin(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            User user = userService.cardLogin(loginRequest);
            return ResponseEntity.ok(ApiResponse.success(user));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.validationError(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("登录失败：" + e.getMessage()));
        }
    }
    
    /**
     * 获取当前用户信息接口
     * POST /users/whoami
     */
    @PostMapping("/whoami")
    public ResponseEntity<ApiResponse<User>> whoami(Authentication authentication) {
        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                throw new IllegalArgumentException("用户未认证");
            }
            String userId = authentication.getName();
            User user = userService.getUserInfo(userId);
            return ResponseEntity.ok(ApiResponse.success(user));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.validationError(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("获取用户信息失败：" + e.getMessage()));
        }
    }

    /**
     * 用户登出接口
     * POST /users/logout
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Object>> logout(Authentication authentication) {
        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                throw new IllegalArgumentException("用户未认证");
            }
            String userId = authentication.getName();
            userService.logout(userId);
            return ResponseEntity.ok(ApiResponse.success("登出成功"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.validationError(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("登出失败：" + e.getMessage()));
        }
    }
}
