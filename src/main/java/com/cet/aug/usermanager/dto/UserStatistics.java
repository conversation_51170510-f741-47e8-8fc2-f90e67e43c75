package com.cet.aug.usermanager.dto;

/**
 * 用户统计信息DTO
 */
public class UserStatistics {
    
    private long totalUsers;           // 总用户数
    private long activeUsers;          // 活跃用户数
    private long inactiveUsers;        // 非活跃用户数
    private long totalVipUsers;        // 总VIP用户数
    private long validVipUsers;        // 有效VIP用户数
    private long expiredVipUsers;      // 过期VIP用户数
    private long nonVipUsers;          // 非VIP用户数
    private long premiumUsers;         // Premium用户数
    private long basicUsers;           // Basic用户数
    private long enterpriseUsers;      // Enterprise用户数
    
    public UserStatistics() {}
    
    public UserStatistics(long totalUsers, long activeUsers, long inactiveUsers, 
                         long totalVipUsers, long validVipUsers, long expiredVipUsers, 
                         long nonVipUsers, long premiumUsers, long basicUsers, long enterpriseUsers) {
        this.totalUsers = totalUsers;
        this.activeUsers = activeUsers;
        this.inactiveUsers = inactiveUsers;
        this.totalVipUsers = totalVipUsers;
        this.validVipUsers = validVipUsers;
        this.expiredVipUsers = expiredVipUsers;
        this.nonVipUsers = nonVipUsers;
        this.premiumUsers = premiumUsers;
        this.basicUsers = basicUsers;
        this.enterpriseUsers = enterpriseUsers;
    }
    
    // Getters and Setters
    public long getTotalUsers() {
        return totalUsers;
    }
    
    public void setTotalUsers(long totalUsers) {
        this.totalUsers = totalUsers;
    }
    
    public long getActiveUsers() {
        return activeUsers;
    }
    
    public void setActiveUsers(long activeUsers) {
        this.activeUsers = activeUsers;
    }
    
    public long getInactiveUsers() {
        return inactiveUsers;
    }
    
    public void setInactiveUsers(long inactiveUsers) {
        this.inactiveUsers = inactiveUsers;
    }
    
    public long getTotalVipUsers() {
        return totalVipUsers;
    }
    
    public void setTotalVipUsers(long totalVipUsers) {
        this.totalVipUsers = totalVipUsers;
    }
    
    public long getValidVipUsers() {
        return validVipUsers;
    }
    
    public void setValidVipUsers(long validVipUsers) {
        this.validVipUsers = validVipUsers;
    }
    
    public long getExpiredVipUsers() {
        return expiredVipUsers;
    }
    
    public void setExpiredVipUsers(long expiredVipUsers) {
        this.expiredVipUsers = expiredVipUsers;
    }
    
    public long getNonVipUsers() {
        return nonVipUsers;
    }
    
    public void setNonVipUsers(long nonVipUsers) {
        this.nonVipUsers = nonVipUsers;
    }
    
    public long getPremiumUsers() {
        return premiumUsers;
    }
    
    public void setPremiumUsers(long premiumUsers) {
        this.premiumUsers = premiumUsers;
    }
    
    public long getBasicUsers() {
        return basicUsers;
    }
    
    public void setBasicUsers(long basicUsers) {
        this.basicUsers = basicUsers;
    }
    
    public long getEnterpriseUsers() {
        return enterpriseUsers;
    }
    
    public void setEnterpriseUsers(long enterpriseUsers) {
        this.enterpriseUsers = enterpriseUsers;
    }
}
