package com.cet.aug.usermanager.controller;

import com.cet.aug.usermanager.dto.LoginRequest;
import com.cet.aug.usermanager.entity.User;
import com.cet.aug.usermanager.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
public class UserControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @org.junit.jupiter.api.BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }
    
    @Test
    public void testCardLogin_Success() throws Exception {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("test123456789", "main");
        User mockUser = new User();
        mockUser.setUserId("test_user");
        mockUser.setToken("mock_token");
        
        // 模拟服务层行为
        when(userService.cardLogin(any(LoginRequest.class))).thenReturn(mockUser);
        
        // 执行测试
        mockMvc.perform(post("/api/users/card-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andExpect(jsonPath("$.data.id").value("test_user"));
    }
    
    @Test
    public void testCardLogin_InvalidRequest() throws Exception {
        // 准备无效的测试数据
        LoginRequest loginRequest = new LoginRequest("", "main");
        
        // 执行测试
        mockMvc.perform(post("/api/users/card-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));
    }
}
