package com.cet.aug.usermanager.service;

import com.cet.aug.usermanager.dto.ActivationCodeRequest;
import com.cet.aug.usermanager.dto.ActivationCodeResponse;
import com.cet.aug.usermanager.dto.LoginRequest;
import com.cet.aug.usermanager.entity.User;
import com.cet.aug.usermanager.entity.VipInfo;
import com.cet.aug.usermanager.mapper.UserMapper;
import com.cet.aug.usermanager.mapper.VipInfoMapper;
import com.cet.aug.usermanager.security.JwtTokenProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserServiceTest {

    @Mock
    private UserMapper userMapper;

    @Mock
    private VipInfoMapper vipInfoMapper;

    @Mock
    private JwtTokenProvider jwtTokenProvider;

    @InjectMocks
    private UserService userService;

    private User mockUser;
    private VipInfo mockVipInfo;

    @BeforeEach
    void setUp() {
        mockVipInfo = new VipInfo();
        mockVipInfo.setId(1L);
        mockVipInfo.setExpireAt(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000); // 1年后过期
        mockVipInfo.setProduct("premium");
        mockVipInfo.setPower(5);
        mockVipInfo.setScore(1000);
        mockVipInfo.setDayScore(100.0f);

        mockUser = new User();
        mockUser.setId(1L);
        mockUser.setUserId("test_user");
        mockUser.setActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO");
        mockUser.setAgent("main");
        mockUser.setStatus("active");
        mockUser.setVip(mockVipInfo);
        mockUser.setVipInfoId(1L);
        mockUser.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void testGenerateActivationCode_Success() {
        // 准备测试数据
        ActivationCodeRequest request = new ActivationCodeRequest();
        request.setExpireDays(365);
        request.setAgent("main");
        request.setProduct("premium");
        request.setPower(5);
        request.setScore(1000);
        request.setDayScore(100.0f);

        // 模拟行为
        when(userMapper.findByActivationCode(anyString())).thenReturn(null); // 激活码不存在
        when(vipInfoMapper.insert(any(VipInfo.class))).thenReturn(1);
        when(userMapper.insert(any(User.class))).thenReturn(1);

        // 执行测试
        ActivationCodeResponse response = userService.generateActivationCode(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getActivationCode());
        assertTrue(response.getActivationCode().matches("[A-Z]{5}-[0-9]{5}-[A-Z]{5}-[0-9]{5}-[A-Z]{5}"));
        assertEquals(365, response.getExpireDays());
        assertNotNull(response.getUser());
        assertNotNull(response.getVipInfo());

        // 验证方法调用
        verify(vipInfoMapper, times(1)).insert(any(VipInfo.class));
        verify(userMapper, times(1)).insert(any(User.class));
    }

    @Test
    void testCardLogin_NonExistentCode() {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("NONEX-12345-ISTENT-67890-CODES", "main");

        // 模拟行为
        when(userMapper.findByActivationCode("NONEX-12345-ISTENT-67890-CODES")).thenReturn(null);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> userService.cardLogin(loginRequest)
        );

        assertEquals("激活码不存在，登录失败", exception.getMessage());
        verify(userMapper, times(1)).findByActivationCode("NONEX-12345-ISTENT-67890-CODES");
    }

    @Test
    void testCardLogin_ExpiredCode() {
        // 准备过期的VIP信息
        VipInfo expiredVipInfo = new VipInfo();
        expiredVipInfo.setId(1L);
        expiredVipInfo.setExpireAt(System.currentTimeMillis() - 24 * 60 * 60 * 1000); // 1天前过期

        User expiredUser = new User();
        expiredUser.setId(1L);
        expiredUser.setUserId("expired_user");
        expiredUser.setActivationCode("EXPIR-12345-EDCOD-67890-ETEST");
        expiredUser.setAgent("main");
        expiredUser.setStatus("active");
        expiredUser.setVip(expiredVipInfo);

        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("EXPIR-12345-EDCOD-67890-ETEST", "main");

        // 模拟行为
        when(userMapper.findByActivationCode("EXPIR-12345-EDCOD-67890-ETEST")).thenReturn(expiredUser);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> userService.cardLogin(loginRequest)
        );

        assertTrue(exception.getMessage().contains("激活码已过期"));
        verify(userMapper, times(1)).findByActivationCode("EXPIR-12345-EDCOD-67890-ETEST");
    }

    @Test
    void testCardLogin_ValidCode() {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("ABCDE-12345-FGHIJ-67890-KLMNO", "main");

        // 模拟行为
        when(userMapper.findByActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO")).thenReturn(mockUser);
        when(userMapper.update(any(User.class))).thenReturn(1);
        when(jwtTokenProvider.generateToken("test_user")).thenReturn("mock_token");

        // 执行测试
        User result = userService.cardLogin(loginRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals("test_user", result.getUserId());
        assertEquals("mock_token", result.getToken());
        assertEquals("ABCDE-12345-FGHIJ-67890-KLMNO", result.getActivationCode());

        // 验证方法调用
        verify(userMapper, times(1)).findByActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO");
        verify(userMapper, times(1)).update(any(User.class));
        verify(jwtTokenProvider, times(1)).generateToken("test_user");
    }

    @Test
    void testCardLogin_AgentMismatch() {
        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("ABCDE-12345-FGHIJ-67890-KLMNO", "different_agent");

        // 模拟行为
        when(userMapper.findByActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO")).thenReturn(mockUser);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> userService.cardLogin(loginRequest)
        );

        assertEquals("代理标识不匹配", exception.getMessage());
        verify(userMapper, times(1)).findByActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO");
    }

    @Test
    void testCardLogin_InactiveUser() {
        // 准备非活跃用户
        mockUser.setStatus("inactive");

        // 准备测试数据
        LoginRequest loginRequest = new LoginRequest("ABCDE-12345-FGHIJ-67890-KLMNO", "main");

        // 模拟行为
        when(userMapper.findByActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO")).thenReturn(mockUser);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> userService.cardLogin(loginRequest)
        );

        assertEquals("用户账户已被禁用", exception.getMessage());
        verify(userMapper, times(1)).findByActivationCode("ABCDE-12345-FGHIJ-67890-KLMNO");
    }

    @Test
    void testGenerateActivationCode_UniqueCodeGeneration() {
        // 准备测试数据
        ActivationCodeRequest request = new ActivationCodeRequest();
        request.setExpireDays(365);

        // 模拟第一次生成的激活码已存在，第二次不存在
        when(userMapper.findByActivationCode(anyString()))
                .thenReturn(mockUser) // 第一次返回存在的用户
                .thenReturn(null);    // 第二次返回null，表示激活码不存在

        when(vipInfoMapper.insert(any(VipInfo.class))).thenReturn(1);
        when(userMapper.insert(any(User.class))).thenReturn(1);

        // 执行测试
        ActivationCodeResponse response = userService.generateActivationCode(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getActivationCode());

        // 验证激活码查询被调用了2次（第一次冲突，第二次成功）
        verify(userMapper, times(2)).findByActivationCode(anyString());
    }
}
