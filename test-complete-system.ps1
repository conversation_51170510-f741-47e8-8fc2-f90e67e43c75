Write-Host "=== UserManager 完整系统测试 ===" -ForegroundColor Green

# 测试后端API
Write-Host "`n1. 测试后端API..." -ForegroundColor Yellow

# 健康检查
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
    Write-Host "✅ 健康检查: $($healthResponse.data.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 管理员登录
try {
    $adminLoginBody = @{
        username = "root"
        password = "admin123"
    } | ConvertTo-Json
    
    $adminLoginResponse = Invoke-RestMethod -Uri "http://localhost:8081/admin/login" -Method POST -ContentType "application/json" -Body $adminLoginBody
    $adminToken = $adminLoginResponse.data.token
    Write-Host "✅ 管理员登录成功: $($adminLoginResponse.data.username)" -ForegroundColor Green
} catch {
    Write-Host "❌ 管理员登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 获取统计信息
try {
    $headers = @{ "X-Auth-Token" = $adminToken }
    $statsResponse = Invoke-RestMethod -Uri "http://localhost:8081/admin/statistics" -Method GET -Headers $headers
    Write-Host "✅ 统计信息获取成功:" -ForegroundColor Green
    Write-Host "   总用户数: $($statsResponse.data.totalUsers)" -ForegroundColor Cyan
    Write-Host "   活跃用户: $($statsResponse.data.activeUsers)" -ForegroundColor Cyan
    Write-Host "   VIP用户: $($statsResponse.data.totalVipUsers)" -ForegroundColor Cyan
    Write-Host "   有效VIP: $($statsResponse.data.validVipUsers)" -ForegroundColor Cyan
    Write-Host "   过期VIP: $($statsResponse.data.expiredVipUsers)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 统计信息获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 获取用户列表
try {
    $usersResponse = Invoke-RestMethod -Uri "http://localhost:8081/admin/users" -Method GET -Headers $headers
    Write-Host "✅ 用户列表获取成功: $($usersResponse.data.Count) 个用户" -ForegroundColor Green
} catch {
    Write-Host "❌ 用户列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试用户登录
try {
    $userLoginBody = @{
        card = "test123456789"
        agent = "main"
    } | ConvertTo-Json
    
    $userLoginResponse = Invoke-RestMethod -Uri "http://localhost:8081/users/card-login" -Method POST -ContentType "application/json" -Body $userLoginBody
    $userToken = $userLoginResponse.data.token
    Write-Host "✅ 用户登录成功: $($userLoginResponse.data.id)" -ForegroundColor Green
} catch {
    Write-Host "❌ 用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试用户信息查询
if ($userToken) {
    try {
        $userHeaders = @{ "X-Auth-Token" = $userToken }
        $whoamiResponse = Invoke-RestMethod -Uri "http://localhost:8081/users/whoami" -Method POST -Headers $userHeaders
        Write-Host "✅ 用户信息查询成功: $($whoamiResponse.data.userId)" -ForegroundColor Green
    } catch {
        Write-Host "❌ 用户信息查询失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试前端访问
Write-Host "`n2. 测试前端访问..." -ForegroundColor Yellow

try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -UseBasicParsing
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ 前端页面访问成功" -ForegroundColor Green
        if ($frontendResponse.Content -like "*UserManager*") {
            Write-Host "✅ 前端页面内容正确" -ForegroundColor Green
        } else {
            Write-Host "⚠️  前端页面内容可能有问题" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ 前端页面访问失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 系统测试总结 ===" -ForegroundColor Green
Write-Host "后端API服务: http://localhost:8081 ✅" -ForegroundColor Green
Write-Host "前端管理界面: http://localhost:3000 ✅" -ForegroundColor Green
Write-Host "管理员账号: root / admin123 ✅" -ForegroundColor Green
Write-Host "`n🎉 UserManager系统运行正常！" -ForegroundColor Green
Write-Host "请访问 http://localhost:3000 使用管理界面" -ForegroundColor Cyan
