Write-Host "Testing login fix..." -ForegroundColor Green

# Test admin login API directly
try {
    $body = '{"username":"root","password":"admin123"}'
    $response = Invoke-RestMethod -Uri "http://localhost:8081/admin/login" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Backend admin login: SUCCESS" -ForegroundColor Green
    Write-Host "Token received: $($response.data.token.Substring(0,20))..." -ForegroundColor Cyan
} catch {
    Write-Host "Backend admin login: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test frontend page access
try {
    $frontend = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -UseBasicParsing
    if ($frontend.StatusCode -eq 200) {
        Write-Host "Frontend page: ACCESSIBLE" -ForegroundColor Green
    }
} catch {
    Write-Host "Frontend page: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nLogin fix completed!" -ForegroundColor Yellow
Write-Host "Please try logging in at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Username: root" -ForegroundColor Cyan
Write-Host "Password: admin123" -ForegroundColor Cyan
