# Test activation code generation and login functionality

Write-Host "=== User Management System API Test ===" -ForegroundColor Green

# 1. Test health check
Write-Host "`n1. Testing health check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri 'http://localhost:8082/health' -Method GET
    Write-Host "Health check success: $($healthResponse.StatusCode)" -ForegroundColor Green
    $healthData = $healthResponse.Content | ConvertFrom-Json
    Write-Host "Application status: $($healthData.data.status)" -ForegroundColor Green
} catch {
    Write-Host "Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Admin login to get token
Write-Host "`n2. Admin login..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "root"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri 'http://localhost:8082/admin/login' -Method POST -Body $loginBody -ContentType 'application/json'
    Write-Host "Admin login success: $($loginResponse.StatusCode)" -ForegroundColor Green
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.data.token
    Write-Host "Got Token: $($token.Substring(0, 20))..." -ForegroundColor Green
} catch {
    Write-Host "Admin login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. Generate activation code
Write-Host "`n3. Generating activation code..." -ForegroundColor Yellow
try {
    $generateBody = @{
        expireDays = 365
        agent = "main"
        product = "premium"
        power = 5
        score = 1000
        dayScore = 100.0
    } | ConvertTo-Json

    $headers = @{
        'Authorization' = "Bearer $token"
        'Content-Type' = 'application/json'
    }

    $generateResponse = Invoke-WebRequest -Uri 'http://localhost:8082/users/generate-activation-code' -Method POST -Body $generateBody -Headers $headers
    Write-Host "Activation code generation success: $($generateResponse.StatusCode)" -ForegroundColor Green
    $generateData = $generateResponse.Content | ConvertFrom-Json
    $activationCode = $generateData.data.activationCode
    Write-Host "Generated activation code: $activationCode" -ForegroundColor Green
    Write-Host "User ID: $($generateData.data.user.userId)" -ForegroundColor Green
    Write-Host "VIP product: $($generateData.data.vipInfo.product)" -ForegroundColor Green
} catch {
    Write-Host "Activation code generation failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorContent = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorContent)
        $errorText = $reader.ReadToEnd()
        Write-Host "Error details: $errorText" -ForegroundColor Red
    }
    exit 1
}

# 4. Test activation code login
Write-Host "`n4. Testing activation code login..." -ForegroundColor Yellow
try {
    $cardLoginBody = @{
        card = $activationCode
        agent = "main"
    } | ConvertTo-Json

    $cardLoginResponse = Invoke-WebRequest -Uri 'http://localhost:8082/users/card-login' -Method POST -Body $cardLoginBody -ContentType 'application/json'
    Write-Host "Activation code login success: $($cardLoginResponse.StatusCode)" -ForegroundColor Green
    $cardLoginData = $cardLoginResponse.Content | ConvertFrom-Json
    Write-Host "Login user ID: $($cardLoginData.data.userId)" -ForegroundColor Green
    Write-Host "VIP status: $($cardLoginData.data.vip.product)" -ForegroundColor Green
} catch {
    Write-Host "Activation code login failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorContent = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorContent)
        $errorText = $reader.ReadToEnd()
        Write-Host "Error details: $errorText" -ForegroundColor Red
    }
}

# 5. Test non-existent activation code
Write-Host "`n5. Testing non-existent activation code..." -ForegroundColor Yellow
try {
    $invalidCardBody = @{
        card = "NONEX-12345-ISTENT-67890-CODES"
        agent = "main"
    } | ConvertTo-Json

    $invalidCardResponse = Invoke-WebRequest -Uri 'http://localhost:8082/users/card-login' -Method POST -Body $invalidCardBody -ContentType 'application/json'
    Write-Host "Unexpected success: Non-existent activation code should not login successfully" -ForegroundColor Red
} catch {
    Write-Host "Correct behavior: Non-existent activation code login failed" -ForegroundColor Green
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "Returned correct 400 status code" -ForegroundColor Green
    }
}

Write-Host "`n=== Test completed ===" -ForegroundColor Green
