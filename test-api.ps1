# UserManager API 测试脚本

Write-Host "=== UserManager API 测试开始 ===" -ForegroundColor Green

# 1. 健康检查测试
Write-Host "`n1. 测试健康检查接口..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:8081/api/health" -Method GET -UseBasicParsing
    Write-Host "健康检查状态码: $($healthResponse.StatusCode)" -ForegroundColor Green
    $healthJson = $healthResponse.Content | ConvertFrom-Json
    Write-Host "应用状态: $($healthJson.data.status)" -ForegroundColor Green
    Write-Host "应用版本: $($healthJson.data.version)" -ForegroundColor Green
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 用户登录测试
Write-Host "`n2. 测试用户登录..." -ForegroundColor Yellow
try {
    $loginBody = @{
        card = "test123456789"
        agent = "main"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:8081/api/users/card-login" -Method POST -ContentType "application/json" -Body $loginBody -UseBasicParsing
    Write-Host "登录状态码: $($loginResponse.StatusCode)" -ForegroundColor Green
    
    $loginJson = $loginResponse.Content | ConvertFrom-Json
    Write-Host "登录结果: $($loginJson.msg)" -ForegroundColor Green
    Write-Host "用户ID: $($loginJson.data.id)" -ForegroundColor Green
    Write-Host "VIP类型: $($loginJson.data.vip.product)" -ForegroundColor Green
    
    # 保存token用于后续测试
    $token = $loginJson.data.token
    Write-Host "Token已获取: $($token.Substring(0, 20))..." -ForegroundColor Green
    
} catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. 获取用户信息测试
Write-Host "`n3. 测试获取用户信息..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-Auth-Token" = $token
    }
    
    $whoamiResponse = Invoke-WebRequest -Uri "http://localhost:8081/api/users/whoami" -Method POST -Headers $headers -UseBasicParsing
    Write-Host "用户信息查询状态码: $($whoamiResponse.StatusCode)" -ForegroundColor Green
    
    $whoamiJson = $whoamiResponse.Content | ConvertFrom-Json
    Write-Host "用户ID: $($whoamiJson.data.id)" -ForegroundColor Green
    Write-Host "激活码: $($whoamiJson.data.activationCode)" -ForegroundColor Green
    Write-Host "用户状态: $($whoamiJson.data.status)" -ForegroundColor Green
    Write-Host "VIP积分: $($whoamiJson.data.vip.score)" -ForegroundColor Green
    
} catch {
    Write-Host "获取用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 用户登出测试
Write-Host "`n4. 测试用户登出..." -ForegroundColor Yellow
try {
    $logoutResponse = Invoke-WebRequest -Uri "http://localhost:8081/api/users/logout" -Method POST -Headers $headers -UseBasicParsing
    Write-Host "登出状态码: $($logoutResponse.StatusCode)" -ForegroundColor Green
    
    $logoutJson = $logoutResponse.Content | ConvertFrom-Json
    Write-Host "登出结果: $($logoutJson.data)" -ForegroundColor Green
    
} catch {
    Write-Host "登出失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试新用户创建（使用不存在的激活码）
Write-Host "`n5. 测试新用户创建..." -ForegroundColor Yellow
try {
    $newUserBody = @{
        card = "newuser123456"
        agent = "main"
    } | ConvertTo-Json
    
    $newUserResponse = Invoke-WebRequest -Uri "http://localhost:8081/api/users/card-login" -Method POST -ContentType "application/json" -Body $newUserBody -UseBasicParsing
    Write-Host "新用户创建状态码: $($newUserResponse.StatusCode)" -ForegroundColor Green
    
    $newUserJson = $newUserResponse.Content | ConvertFrom-Json
    Write-Host "新用户ID: $($newUserJson.data.id)" -ForegroundColor Green
    Write-Host "新用户VIP类型: $($newUserJson.data.vip.product)" -ForegroundColor Green
    
} catch {
    Write-Host "新用户创建失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试参数验证（空激活码）
Write-Host "`n6. 测试参数验证..." -ForegroundColor Yellow
try {
    $invalidBody = @{
        card = ""
        agent = "main"
    } | ConvertTo-Json
    
    $invalidResponse = Invoke-WebRequest -Uri "http://localhost:8081/api/users/card-login" -Method POST -ContentType "application/json" -Body $invalidBody -UseBasicParsing
    Write-Host "参数验证测试意外成功" -ForegroundColor Red
    
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "参数验证正常工作 - 返回400错误" -ForegroundColor Green
    } else {
        Write-Host "参数验证测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== UserManager API 测试完成 ===" -ForegroundColor Green
