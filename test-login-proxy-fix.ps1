Write-Host "Testing login proxy fix..." -ForegroundColor Green

# Test direct backend API
Write-Host "`n1. Testing direct backend API..." -ForegroundColor Yellow
try {
    $body = '{"username":"root","password":"admin123"}'
    $response = Invoke-RestMethod -Uri "http://localhost:8081/admin/login" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Direct backend login: SUCCESS" -ForegroundColor Green
    Write-Host "Token: $($response.data.token.Substring(0,20))..." -ForegroundColor Cyan
} catch {
    Write-Host "Direct backend login: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test frontend proxy
Write-Host "`n2. Testing frontend proxy..." -ForegroundColor Yellow
try {
    $body = '{"username":"root","password":"admin123"}'
    $response = Invoke-RestMethod -Uri "http://localhost:3000/api/admin/login" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Frontend proxy login: SUCCESS" -ForegroundColor Green
    Write-Host "Token: $($response.data.token.Substring(0,20))..." -ForegroundColor Cyan
} catch {
    Write-Host "Frontend proxy login: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test frontend page
Write-Host "`n3. Testing frontend page..." -ForegroundColor Yellow
try {
    $frontend = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -UseBasicParsing
    if ($frontend.StatusCode -eq 200) {
        Write-Host "Frontend page: ACCESSIBLE" -ForegroundColor Green
    }
} catch {
    Write-Host "Frontend page: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nProxy fix completed!" -ForegroundColor Yellow
Write-Host "Please try logging in again at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Username: root" -ForegroundColor Cyan
Write-Host "Password: admin123" -ForegroundColor Cyan
